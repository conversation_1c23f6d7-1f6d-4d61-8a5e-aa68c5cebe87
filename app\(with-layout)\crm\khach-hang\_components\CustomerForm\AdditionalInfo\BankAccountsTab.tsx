import { useFieldArray, useFormContext } from 'react-hook-form';
import { Button, Table } from 'reactstrap';

const BankAccountsTab = () => {
    const { control, watch, setValue } = useFormContext();

    const tdStyle = {
        borderLeft: 'none',
        borderRight: 'none',
        borderTop: '1px solid #dee2e6',
        borderBottom: '1px solid #dee2e6',
    };

    const { fields, append, remove } = useFieldArray({
        control,
        name: 'addBankAccountDtos',
    });
    const bankAccounts = watch('addBankAccountDtos') || [];

    const handleInputChange = (index: number, field: string, value: string) => {
        setValue(`addBankAccountDtos.${index}.${field}`, value);
    };

    const handleAdd = () => {
        append({
            bank: '',
            bankBranch: '',
            accountNumber: '',
            accountHolderName: '',
            customSwiftCode: '',
        });
    };

    const handleDeleteBankAccount = (index: number) => {
        remove(index);
    };

    return (
        <>
            <div className='d-flex justify-content-between mt-3 mb-3'>
                <div style={{ paddingTop: '10px' }}>
                    <h5 className='mb-0'>Tài khoản thanh toán</h5>
                </div>
                <Button
                    className='btn-sm'
                    onClick={handleAdd}
                    style={{
                        borderColor: '#0ab39c',
                        backgroundColor: '#ffffff',
                        color: '#0ab39c',
                    }}
                >
                    <i className='ri-add-line align-bottom me-1'></i> Thêm tài
                    khoản
                </Button>
            </div>
            <div className='mt-3'>
                <Table responsive className='align-middle'>
                    <thead style={{ backgroundColor: '#f3f6f9' }}>
                        <tr>
                            <th
                                style={{
                                    width: '5%',
                                    borderLeft: 'none',
                                    borderRight: 'none',
                                    borderTop: '1px solid #dee2e6',
                                }}
                            >
                                Dòng
                            </th>
                            <th
                                style={{
                                    width: '12%',
                                    borderLeft: 'none',
                                    borderRight: 'none',
                                    borderTop: '1px solid #dee2e6',
                                }}
                            >
                                Ngân hàng
                            </th>
                            <th
                                style={{
                                    width: '30%',
                                    borderLeft: 'none',
                                    borderRight: 'none',
                                    borderTop: '1px solid #dee2e6',
                                }}
                            >
                                Chi nhánh
                            </th>
                            <th
                                style={{
                                    width: '15%',
                                    borderLeft: 'none',
                                    borderRight: 'none',
                                    borderTop: '1px solid #dee2e6',
                                }}
                            >
                                Số tài khoản
                            </th>
                            <th
                                style={{
                                    width: '15%',
                                    borderLeft: 'none',
                                    borderRight: 'none',
                                    borderTop: '1px solid #dee2e6',
                                }}
                            >
                                Tên chủ tài khoản
                            </th>
                            <th
                                style={{
                                    width: '15%',
                                    borderLeft: 'none',
                                    borderRight: 'none',
                                    borderTop: '1px solid #dee2e6',
                                }}
                            >
                                Mã Swift
                            </th>
                            <th
                                style={{
                                    width: '8%',
                                    borderLeft: 'none',
                                    borderRight: 'none',
                                    borderTop: '1px solid #dee2e6',
                                }}
                            ></th>
                        </tr>
                    </thead>
                    <tbody>
                        {fields.map((field, index) => {
                            return (
                                <tr key={field.id}>
                                    <td style={tdStyle}>{index + 1}</td>
                                    <td style={tdStyle}>
                                        <input
                                            type='text'
                                            value={
                                                bankAccounts[index]?.bank || ''
                                            }
                                            onChange={(e) =>
                                                handleInputChange(
                                                    index,
                                                    'bank',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder='Nhập tên ngân hàng'
                                            style={{
                                                padding: '0px',
                                                width: '100%',
                                                border: 'none',
                                                outline: 'none',
                                                background: 'transparent',
                                            }}
                                        />
                                    </td>
                                    <td style={tdStyle}>
                                        <input
                                            type='text'
                                            value={
                                                bankAccounts[index]
                                                    ?.bankBranch || ''
                                            }
                                            onChange={(e) =>
                                                handleInputChange(
                                                    index,
                                                    'bankBranch',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder='Nhập chi nhánh'
                                            style={{
                                                padding: '0px',
                                                width: '100%',
                                                border: 'none',
                                                outline: 'none',
                                                background: 'transparent',
                                            }}
                                        />
                                    </td>
                                    <td style={tdStyle}>
                                        <input
                                            type='text'
                                            value={
                                                bankAccounts[index]
                                                    ?.accountNumber || ''
                                            }
                                            onChange={(e) =>
                                                handleInputChange(
                                                    index,
                                                    'accountNumber',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder='Nhập số tài khoản'
                                            style={{
                                                padding: '0px',
                                                width: '100%',
                                                border: 'none',
                                                outline: 'none',
                                                background: 'transparent',
                                            }}
                                        />
                                    </td>
                                    <td style={tdStyle}>
                                        <input
                                            type='text'
                                            value={
                                                bankAccounts[index]
                                                    ?.accountHolderName || ''
                                            }
                                            onChange={(e) =>
                                                handleInputChange(
                                                    index,
                                                    'accountHolderName',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder='Nhập tên chủ tài khoản'
                                            style={{
                                                padding: '0px',
                                                width: '100%',
                                                border: 'none',
                                                outline: 'none',
                                                background: 'transparent',
                                            }}
                                        />
                                    </td>
                                    <td style={tdStyle}>
                                        <input
                                            type='text'
                                            value={
                                                bankAccounts[index]
                                                    ?.customSwiftCode || ''
                                            }
                                            onChange={(e) =>
                                                handleInputChange(
                                                    index,
                                                    'customSwiftCode',
                                                    e.target.value,
                                                )
                                            }
                                            placeholder='Nhập mã Swift'
                                            style={{
                                                padding: '0px',
                                                width: '100%',
                                                border: 'none',
                                                outline: 'none',
                                                background: 'transparent',
                                            }}
                                        />
                                    </td>
                                    <td style={tdStyle}>
                                        <Button
                                            color='danger'
                                            size='sm'
                                            onClick={() =>
                                                handleDeleteBankAccount(index)
                                            }
                                        >
                                            <i className='ri-delete-bin-line'></i>
                                        </Button>
                                    </td>
                                </tr>
                            );
                        })}
                    </tbody>
                </Table>
            </div>
        </>
    );
};

export default BankAccountsTab;

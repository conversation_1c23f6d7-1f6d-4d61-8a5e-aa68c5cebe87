'use client';

import { useCreatePartner } from '@/apis/partners/partners.api';
import { IPartnerPayload } from '@/apis/partners/partners.type';
import FormController from '@/components/common/FormController';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import {
    Button,
    Col,
    Modal,
    ModalBody,
    ModalFooter,
    ModalHeader,
} from 'reactstrap';

interface AddPartnerModalProps {
    isOpen: boolean;
    toggle: () => void;
    onSuccess?: () => void;
}

const AddPartnerModal = ({
    isOpen,
    toggle,
    onSuccess,
}: AddPartnerModalProps) => {
    const methods = useForm<IPartnerPayload>({
        defaultValues: {
            addTradePartnerDto: {
                name: '',
                shortName: '',
                email: '',
                phoneNumber: '',
                taxCode: '',
                address: '',
                addBankAccountDtos: [],
            },
            contactIds: [],
        },
    });

    const nameValue = methods.watch('addTradePartnerDto.name');

    React.useEffect(() => {
        methods.setValue('addTradePartnerDto.shortName', nameValue);
    }, [nameValue, methods]);

    const { mutate: createPartner } = useCreatePartner({
        onSuccess: () => {
            toast.success('Tạo mới đối tác thành công');
            methods.reset();
            toggle();
            onSuccess?.();
        },
        onError: () => {
            toast.error('Có lỗi xảy ra khi thêm đối tác');
        },
    });
    const handleSubmit = methods.handleSubmit((data: IPartnerPayload) => {
        createPartner(data);
    });

    return (
        <Modal isOpen={isOpen} toggle={toggle}>
            <FormProvider {...methods}>
                <ModalHeader toggle={toggle}>
                    Tạo đối tác thương mại
                </ModalHeader>
                <ModalBody>
                    <Col md='12'>
                        <FormController
                            controlType='textInput'
                            name='addTradePartnerDto.name'
                            label='Tên đối tác thương mại'
                            placeholder='Nhập tên đối tác thương mại'
                            required={true}
                        />
                    </Col>

                    <Col md='12'>
                        <FormController
                            controlType='textInput'
                            name='addTradePartnerDto.taxCode'
                            label='Mã số thuế'
                            placeholder='Nhập mã số thuế'
                            required={false}
                        />
                    </Col>

                    <Col md='12'>
                        <FormController
                            controlType='textInput'
                            name='addTradePartnerDto.email'
                            label='Email'
                            placeholder='Nhập địa chỉ email'
                            required={false}
                        />
                    </Col>

                    <Col md='12'>
                        <FormController
                            controlType='textInput'
                            name='addTradePartnerDto.phoneNumber'
                            label='Số điện thoại'
                            placeholder='Nhập số điện thoại'
                            required={false}
                        />
                    </Col>
                </ModalBody>
                <ModalFooter>
                    <Button color='secondary' onClick={toggle}>
                        Hủy
                    </Button>
                    <Button color='primary' onClick={handleSubmit}>
                        Tạo mới
                    </Button>
                </ModalFooter>
            </FormProvider>
        </Modal>
    );
};

export default AddPartnerModal;

import { useFieldArray, useFormContext } from 'react-hook-form';
import {
    Button,
    Dropdown,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
} from 'reactstrap';
import { useState } from 'react';
import { ICustomer } from '@/apis/customer/customer.type';
import { IPartnerResponse } from '@/apis/partners/partners.type';

interface PartnersTableProps {
    partnerList: IPartnerResponse[];
    toggleAddPartnerModal: () => void;
}

const PartnersTable = ({
    partnerList,
    toggleAddPartnerModal,
}: PartnersTableProps) => {
    const [partnerDropdownOpen, setPartnerDropdownOpen] = useState<
        number | null
    >(null);

    const { control } = useFormContext<ICustomer>();
    const { fields, append, remove, replace } = useFieldArray({
        control,
        name: 'tradePartners',
    });

    const togglePartnerDropdown = (index: number) => {
        setPartnerDropdownOpen(partnerDropdownOpen === index ? null : index);
    };

    const handleAddPartner = () => {
        append({
            tradePartnerId: '',
        });
    };

    const handleSelectPartners = (index: number, partner: IPartnerResponse) => {
        const currentFields = [...fields];
        currentFields[index] = {
            ...currentFields[index],
            tradePartnerId: partner.id,
        };
        replace(currentFields);
        setPartnerDropdownOpen(null);
    };

    const findPartnerInfo = (partnerId: string) => {
        const fromPartnerList = partnerList.find((p) => p.id === partnerId);
        if (fromPartnerList) {
            return fromPartnerList;
        }

        return null;
    };

    return (
        <div className='mt-4'>
            <div className='d-flex justify-content-between align-items-center mb-3'>
                <h5 className='mb-0'>Đối tác thương mại</h5>
                <Button
                    color='light'
                    size='sm'
                    className='d-flex align-items-center'
                    onClick={handleAddPartner}
                    style={{
                        border: '1px solid #60cfbf',
                        backgroundColor: '#ffffff',
                        color: '#60cfbf',
                        fontSize: '12px',
                    }}
                >
                    <i className='ri-add-line me-1'></i> Thêm đối tác thương mại
                </Button>
            </div>
            <table className='table'>
                <thead style={{ backgroundColor: '#f3f6f9' }}>
                    <tr>
                        <th style={{ width: '8%' }}>Dòng</th>
                        <th style={{ width: '30%' }}>Tên đối tác thương mại</th>
                        <th style={{ width: '15%' }}>Mã số thuế</th>
                        <th style={{ width: '30%' }}>Email</th>
                        <th style={{ width: '12%' }}>Số điện thoại</th>
                        <th style={{ width: '5%' }}></th>
                    </tr>
                </thead>
                <tbody>
                    {fields.map((field, index) => (
                        <tr key={field.id}>
                            <td>{index + 1}</td>
                            <td>
                                <Dropdown
                                    isOpen={partnerDropdownOpen === index}
                                    toggle={() => togglePartnerDropdown(index)}
                                >
                                    <DropdownToggle
                                        tag='div'
                                        style={{
                                            cursor: 'pointer',
                                            border: 'none',
                                        }}
                                    >
                                        {findPartnerInfo(field.tradePartnerId)
                                            ?.name || 'Chọn đối tác'}
                                    </DropdownToggle>
                                    <DropdownMenu
                                        style={{
                                            width: '300px',
                                            maxHeight: '200px',
                                            overflowY: 'auto',
                                        }}
                                    >
                                        {partnerList
                                            .filter(
                                                (option) =>
                                                    !fields.some(
                                                        (field) =>
                                                            field.tradePartnerId ===
                                                            option.id,
                                                    ),
                                            )
                                            .map((option, optIndex) => (
                                                <DropdownItem
                                                    key={optIndex}
                                                    onClick={() =>
                                                        handleSelectPartners(
                                                            index,
                                                            option,
                                                        )
                                                    }
                                                >
                                                    <div>
                                                        <strong>
                                                            {option.name}
                                                        </strong>
                                                    </div>
                                                    <div className='text-muted small'>
                                                        {option.name} -{' '}
                                                        {option.taxCode}
                                                    </div>
                                                </DropdownItem>
                                            ))}
                                        <DropdownItem divider />
                                        <DropdownItem
                                            onClick={toggleAddPartnerModal}
                                        >
                                            <div className='text-primary'>
                                                <i className='ri-add-line me-1'></i>
                                                Thêm đối tác
                                            </div>
                                        </DropdownItem>
                                    </DropdownMenu>
                                </Dropdown>
                            </td>
                            <td>
                                {findPartnerInfo(field.tradePartnerId)
                                    ?.taxCode || ''}
                            </td>
                            <td>
                                {findPartnerInfo(field.tradePartnerId)?.email ||
                                    ''}
                            </td>
                            <td>
                                {findPartnerInfo(field.tradePartnerId)
                                    ?.phoneNumber || ''}
                            </td>
                            <td>
                                <button
                                    type='button'
                                    className='btn btn-sm btn-danger'
                                    onClick={() => remove(index)}
                                >
                                    <i className='ri-delete-bin-line'></i>
                                </button>
                            </td>
                        </tr>
                    ))}
                    {fields.length === 0 && (
                        <tr>
                            <td colSpan={6} className='text-center py-3'>
                                Chưa có dữ liệu. Vui lòng thêm đối tác thương
                                mại.
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>
        </div>
    );
};

export default PartnersTable;

'use client';

import {
    <PERSON><PERSON>,
    Card,
    CardBody,
    CardHeader,
    Col,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Label,
    Nav,
    NavItem,
    NavLink,
    Row,
    TabContent,
    TabPane,
    Table,
    UncontrolledDropdown,
} from 'reactstrap';
import Evaluate from '../evaluate';
import FileInfo from '../file-info';
import { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
    useDeleteCustomer,
    useGetDetailCustomer,
} from '@/apis/customer/customer.api';
import { toast } from 'react-toastify';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { ROUTES } from '@/lib/routes';
import DeleteConfirmModal from '../../_components/Modal/DeleteConfirmModal';
import { AssociatedInfoType } from '@/constants/sharedData/sharedData.enums';
import FormatTextDetail from '@/components/common/FormatTextDetail';

export default function BusinessDetails() {
    const params = useParams();
    const router = useRouter();
    const id = params.id as string;
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const { mutate: deleteCustomer, isPending } = useDeleteCustomer({
        onSuccess: () => {
            toast.success('Xóa khách hàng thành công');
            setIsDeleteModalOpen(false);
            router.push(ROUTES.CRM.PERSONAL.INDEX);
        },
        onError: (error) => {
            toast.error(error.message);
        },
    });
    const { data } = useGetDetailCustomer(id);
    const [activeTab, setActiveTab] = useState('1');
    const toggleTab = (tab: string) => {
        if (activeTab !== tab) {
            setActiveTab(tab);
        }
    };
    const handleConfirmDelete = () => {
        deleteCustomer({ ids: [id] });
    };

    const associatedInfos = data?.data?.associatedInfos || [];
    const phones = associatedInfos.filter(
        (info) => info.associatedInfoType === AssociatedInfoType.PhoneNumber,
    );
    const emails = associatedInfos.filter(
        (info) => info.associatedInfoType === AssociatedInfoType.Email,
    );

    const [showAllPhones, setShowAllPhones] = useState(false);
    const [showAllEmails, setShowAllEmails] = useState(false);
    return (
        <div>
            <Row>
                <Col lg={8}>
                    <Col lg={12}>
                        <Card>
                            <CardHeader className='pb-0 pt-0'>
                                <Nav tabs className='border-bottom'>
                                    <NavItem>
                                        <NavLink
                                            className={`px-4 py-3`}
                                            onClick={() => toggleTab('1')}
                                            style={{
                                                cursor: 'pointer',
                                                borderRadius: '0',
                                                border: 'none',
                                                transition: 'all 0.2s ease',
                                                fontWeight: 500,
                                                marginBottom: '-1px',
                                                backgroundColor: 'transparent',
                                                borderBottom:
                                                    activeTab === '1'
                                                        ? '2px solid #0ab39c'
                                                        : 'none',
                                                fontSize: '14px',
                                                color:
                                                    activeTab === '1'
                                                        ? '#0ab39c'
                                                        : '#212529',
                                            }}
                                        >
                                            Thông tin chung
                                        </NavLink>
                                    </NavItem>
                                    <NavItem>
                                        <NavLink
                                            className={`px-4 py-3`}
                                            onClick={() => toggleTab('2')}
                                            style={{
                                                cursor: 'pointer',
                                                borderRadius: '0',
                                                border: 'none',
                                                transition: 'all 0.2s ease',
                                                fontWeight: 500,
                                                marginBottom: '-1px',
                                                backgroundColor: 'transparent',
                                                borderBottom:
                                                    activeTab === '2'
                                                        ? '2px solid #0ab39c'
                                                        : 'none',
                                                fontSize: '14px',
                                                color:
                                                    activeTab === '2'
                                                        ? '#0ab39c'
                                                        : '#212529',
                                            }}
                                        >
                                            Địa chỉ
                                        </NavLink>
                                    </NavItem>
                                    <NavItem>
                                        <NavLink
                                            className={`px-4 py-3`}
                                            onClick={() => toggleTab('3')}
                                            style={{
                                                cursor: 'pointer',
                                                borderRadius: '0',
                                                border: 'none',
                                                transition: 'all 0.2s ease',
                                                fontWeight: 500,
                                                marginBottom: '-1px',
                                                backgroundColor: 'transparent',
                                                borderBottom:
                                                    activeTab === '3'
                                                        ? '2px solid #0ab39c'
                                                        : 'none',
                                                fontSize: '14px',
                                                color:
                                                    activeTab === '3'
                                                        ? '#0ab39c'
                                                        : '#212529',
                                            }}
                                        >
                                            Tài khoản thanh toán
                                        </NavLink>
                                    </NavItem>
                                    <div className='flex-grow-1'></div>
                                    <div className='d-flex gap-3 align-items-center pe-3 '>
                                        <Button
                                            className='d-flex align-items-center gap-2 btn-outline-primary hover-primary'
                                            style={{
                                                padding: '4px 8px',
                                                fontSize: '12px',
                                                transition: 'all 0.2s ease',
                                                backgroundColor: 'transparent',
                                                border: '1px solid #0ab39c',
                                                color: '#0ab39c',
                                                borderRadius: '6px',
                                                height: '32px',
                                                fontWeight: 500,
                                            }}
                                        >
                                            <i className='ri-pencil-line'></i>
                                            Chỉnh sửa
                                        </Button>
                                        <UncontrolledDropdown>
                                            <DropdownToggle
                                                tag='button'
                                                className='btn'
                                                style={{
                                                    backgroundColor: '#0ab39c',
                                                    border: 'none',
                                                    padding: '4px',
                                                    minWidth: '30px',
                                                }}
                                            >
                                                <i
                                                    className='ri-more-fill'
                                                    style={{
                                                        color: 'white',
                                                    }}
                                                ></i>
                                            </DropdownToggle>
                                            <DropdownMenu end>
                                                <DropdownItem>
                                                    <i className='ri-user-received-line me-2'></i>
                                                    Bàn giao cá nhân
                                                </DropdownItem>
                                                <DropdownItem>
                                                    <i className='ri-history-line me-2'></i>
                                                    Nhật ký hoạt động
                                                </DropdownItem>
                                                <DropdownItem
                                                    className='text-danger'
                                                    onClick={() =>
                                                        setIsDeleteModalOpen(
                                                            true,
                                                        )
                                                    }
                                                >
                                                    <i className='ri-delete-bin-line me-2'></i>
                                                    Xóa cá nhân
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </UncontrolledDropdown>
                                    </div>
                                </Nav>
                            </CardHeader>
                            <CardBody>
                                <TabContent activeTab={activeTab}>
                                    <TabPane tabId='1'>
                                        <Row
                                            style={{
                                                padding: '0px 20px 0px 20px',
                                            }}
                                        >
                                            <Col lg={12} className='mb-2'>
                                                <Row>
                                                    <Col lg={1}>
                                                        <div className='relative inline-block'>
                                                            <div
                                                                className='flex items-center justify-center rounded-full font-bold d-flex justify-content-center align-items-center'
                                                                style={{
                                                                    width: '60px',
                                                                    height: '60px',
                                                                    backgroundColor:
                                                                        '#daf4f0',
                                                                    color: '#0ab39c',
                                                                    borderRadius:
                                                                        '50%',
                                                                    fontSize:
                                                                        '20px',
                                                                }}
                                                            >
                                                                {data?.data?.name
                                                                    ?.charAt(0)
                                                                    .toUpperCase()}
                                                            </div>

                                                            <span className='absolute right-0 bottom-0 w-4 h-4 bg-green-500 rounded-full border-2 border-white'></span>
                                                        </div>
                                                    </Col>
                                                    <Col lg={7}>
                                                        <Label
                                                            style={{
                                                                fontSize:
                                                                    '15px',
                                                            }}
                                                        >
                                                            {data?.data?.name}
                                                        </Label>
                                                        <p>
                                                            {
                                                                data?.data
                                                                    ?.formOfPurchaseName
                                                            }
                                                        </p>
                                                    </Col>
                                                </Row>
                                            </Col>
                                            <Col lg='4'>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='MÃ KHÁCH HÀNG'
                                                        p={
                                                            data?.data?.code ||
                                                            '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='MÃ SỐ THUẾ'
                                                        p={
                                                            data?.data
                                                                ?.taxCode || '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='DOANH THU ƯỚC TÍNH'
                                                        p={
                                                            data?.data
                                                                ?.annualRevenue
                                                                ? `${data?.data?.annualRevenue?.toLocaleString()} (VNĐ)`
                                                                : '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='WEBSITE'
                                                        p={
                                                            data?.data
                                                                ?.website || '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='LINKEDIN PAGE'
                                                        p={
                                                            data?.data
                                                                ?.linkedInPage ||
                                                            '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='ĐIỂM TIỀM NĂNG'
                                                        p={
                                                            data?.data?.leadStatus?.toString() ||
                                                            '-'
                                                        }
                                                    />
                                                </Col>
                                            </Col>

                                            <Col lg='4'>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='LOẠI HÌNH'
                                                        p={
                                                            data?.data
                                                                ?.businessTypeName ||
                                                            '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='SỐ LƯỢNG NHÂN SỰ'
                                                        p={
                                                            data?.data?.numberOfEmployees?.toString() ||
                                                            '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <Label
                                                        className='text-muted'
                                                        style={{
                                                            fontSize: '13px',
                                                        }}
                                                    >
                                                        EMAIL
                                                    </Label>
                                                    <p
                                                        style={{
                                                            fontSize: '13px',
                                                        }}
                                                    >
                                                        {emails.length === 0 ? (
                                                            '-'
                                                        ) : (
                                                            <>
                                                                <div
                                                                    style={{
                                                                        display:
                                                                            'flex',
                                                                        alignItems:
                                                                            'center',
                                                                        gap: '8px',
                                                                    }}
                                                                >
                                                                    <span>
                                                                        {
                                                                            emails[0]
                                                                                .value
                                                                        }
                                                                    </span>
                                                                    {emails.length >
                                                                        1 && (
                                                                        <Button
                                                                            color='link'
                                                                            size='sm'
                                                                            style={{
                                                                                padding: 0,
                                                                                fontSize: 12,
                                                                                minWidth:
                                                                                    'auto',
                                                                            }}
                                                                            onClick={() =>
                                                                                setShowAllEmails(
                                                                                    !showAllEmails,
                                                                                )
                                                                            }
                                                                        >
                                                                            <span className='d-flex align-items-center gap-1'>
                                                                                ({
                                                                                    emails.length
                                                                                })
                                                                                {showAllEmails ? (
                                                                                    <ChevronUp
                                                                                        size={
                                                                                            16
                                                                                        }
                                                                                    />
                                                                                ) : (
                                                                                    <ChevronDown
                                                                                        size={
                                                                                            16
                                                                                        }
                                                                                    />
                                                                                )}
                                                                            </span>
                                                                        </Button>
                                                                    )}
                                                                </div>
                                                                {showAllEmails &&
                                                                    emails
                                                                        .slice(
                                                                            1,
                                                                        )
                                                                        .map(
                                                                            (
                                                                                info,
                                                                            ) => (
                                                                                <div
                                                                                    key={
                                                                                        info.id
                                                                                    }
                                                                                    style={{
                                                                                        marginTop:
                                                                                            '4px',
                                                                                    }}
                                                                                >
                                                                                    {
                                                                                        info.value
                                                                                    }
                                                                                </div>
                                                                            ),
                                                                        )}
                                                            </>
                                                        )}
                                                    </p>
                                                </Col>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='FACEBOOK'
                                                        p={
                                                            data?.data
                                                                ?.facebook ||
                                                            '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='NGÀY THÀNH LẬP'
                                                        p={
                                                            data?.data
                                                                ?.createdDate
                                                                ? new Date(
                                                                      data.data.createdDate,
                                                                  ).toLocaleDateString(
                                                                      'vi-VN',
                                                                  )
                                                                : '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <Label
                                                        className='text-muted'
                                                        style={{
                                                            fontSize: '15px',
                                                        }}
                                                    >
                                                        GIAI ĐOẠN
                                                    </Label>
                                                    <p
                                                        style={{
                                                            fontSize: '14px',
                                                        }}
                                                    >
                                                        <span
                                                            className='d-inline-block'
                                                            style={{
                                                                padding:
                                                                    '2px 8px',
                                                                backgroundColor:
                                                                    'rgba(10, 179, 156, 0.18)',
                                                                color: '#0ab39c',
                                                                borderRadius:
                                                                    '4px',
                                                                fontSize:
                                                                    '12px',
                                                                fontWeight: 500,
                                                                marginTop:
                                                                    '4px',
                                                            }}
                                                        >
                                                            {data?.data
                                                                ?.lifecycleStageEnumName ||
                                                                '-'}
                                                        </span>
                                                    </p>
                                                </Col>
                                            </Col>

                                            <Col lg='4'>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='LĨNH VỰC'
                                                        p={
                                                            data?.data
                                                                ?.industryName ||
                                                            '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='NĂM THÀNH LẬP'
                                                        p={
                                                            data?.data?.dateOfEstablishment?.toString() ||
                                                            '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <Label
                                                        className='text-muted'
                                                        style={{
                                                            fontSize: '15px',
                                                        }}
                                                    >
                                                        SỐ ĐIỆN THOẠI
                                                    </Label>
                                                    <p
                                                        style={{
                                                            fontSize: '14px',
                                                        }}
                                                    >
                                                        {phones.length === 0 ? (
                                                            '-'
                                                        ) : (
                                                            <>
                                                                {(showAllPhones
                                                                    ? phones
                                                                    : phones.slice(
                                                                          0,
                                                                          1,
                                                                      )
                                                                ).map(
                                                                    (info) => (
                                                                        <div
                                                                            key={
                                                                                info.id
                                                                            }
                                                                        >
                                                                            {
                                                                                info.value
                                                                            }
                                                                        </div>
                                                                    ),
                                                                )}
                                                                {phones.length >
                                                                    1 && (
                                                                    <Button
                                                                        color='link'
                                                                        size='sm'
                                                                        style={{
                                                                            padding: 0,
                                                                            fontSize: 12,
                                                                        }}
                                                                        onClick={() =>
                                                                            setShowAllPhones(
                                                                                !showAllPhones,
                                                                            )
                                                                        }
                                                                    >
                                                                        {showAllPhones
                                                                            ? 'Ẩn bớt ▲'
                                                                            : `Xem thêm (${phones.length - 1}) ▼`}
                                                                    </Button>
                                                                )}
                                                            </>
                                                        )}
                                                    </p>
                                                </Col>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='YOUTUBE'
                                                        p={
                                                            data?.data
                                                                ?.youtube || '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <FormatTextDetail
                                                        label='NHÂN VIÊN KINH DOANH'
                                                        p={
                                                            data?.data
                                                                ?.ownerName ||
                                                            '-'
                                                        }
                                                    />
                                                </Col>
                                                <Col md={12}>
                                                    <Label
                                                        className='text-muted'
                                                        style={{
                                                            fontSize: '15px',
                                                        }}
                                                    >
                                                        TRẠNG THÁI
                                                    </Label>
                                                    <p
                                                        style={{
                                                            fontSize: '14px',
                                                        }}
                                                    >
                                                        <span
                                                            className='d-inline-block'
                                                            style={{
                                                                padding:
                                                                    '2px 8px',
                                                                backgroundColor:
                                                                    'rgba(10, 179, 156, 0.18)',
                                                                color: '#0ab39c',
                                                                borderRadius:
                                                                    '4px',
                                                                fontSize:
                                                                    '12px',
                                                                fontWeight: 500,
                                                                marginTop:
                                                                    '4px',
                                                            }}
                                                        >
                                                            {data?.data
                                                                ?.leadStatusName ||
                                                                '-'}
                                                        </span>
                                                    </p>
                                                </Col>
                                            </Col>
                                        </Row>
                                    </TabPane>
                                    <TabPane tabId='2'>
                                        <div>
                                            <Row className='g-3'>
                                                {data?.data?.addresses?.map(
                                                    (address) => (
                                                        <Col
                                                            lg={4}
                                                            key={address.id}
                                                        >
                                                            <Card
                                                                className='h-100'
                                                                style={{
                                                                    border:
                                                                        address.addressType ===
                                                                        1
                                                                            ? '1.5px solid #0ab39c'
                                                                            : '1px solid #e3e3e3',
                                                                }}
                                                            >
                                                                <CardBody>
                                                                    <div className='d-flex align-items-center mb-2'>
                                                                        <i
                                                                            className='ri-contacts-book-2-line'
                                                                            style={{
                                                                                fontSize: 22,
                                                                                marginRight: 8,
                                                                            }}
                                                                        ></i>
                                                                        <span
                                                                            style={{
                                                                                fontWeight: 600,
                                                                                fontSize: 18,
                                                                            }}
                                                                        >
                                                                            {
                                                                                address.addressTypeName
                                                                            }
                                                                        </span>
                                                                    </div>
                                                                    <div
                                                                        style={{
                                                                            fontWeight: 500,
                                                                            fontSize: 15,
                                                                        }}
                                                                    >
                                                                        {address.companyName && (
                                                                            <div>
                                                                                {
                                                                                    address.companyName
                                                                                }
                                                                            </div>
                                                                        )}
                                                                        {
                                                                            address.addressName
                                                                        }
                                                                        {address.wardName &&
                                                                            ` - ${address.wardName}`}
                                                                        {address.districtName &&
                                                                            ` - ${address.districtName}`}
                                                                        {address.provinceName &&
                                                                            ` - ${address.provinceName}`}
                                                                        {address.country &&
                                                                            ` - ${address.country}`}
                                                                    </div>
                                                                </CardBody>
                                                            </Card>
                                                        </Col>
                                                    ),
                                                )}
                                            </Row>
                                        </div>
                                    </TabPane>
                                    <TabPane tabId='3'>
                                        <div className='table-responsive'>
                                            <h5 className='mt-2 mb-2'>
                                                TÀI KHOẢN THANH TOÁN
                                            </h5>
                                            <Table className='table-nowrap align-middle mb-2'>
                                                <thead
                                                    style={{
                                                        backgroundColor:
                                                            '#f3f6f9',
                                                    }}
                                                >
                                                    <tr>
                                                        <th
                                                            scope='col'
                                                            className='p-3'
                                                            style={{
                                                                width: '50px',
                                                            }}
                                                        >
                                                            Dòng
                                                        </th>
                                                        <th
                                                            scope='col'
                                                            className='p-3'
                                                        >
                                                            Ngân hàng
                                                        </th>
                                                        <th
                                                            scope='col'
                                                            className='p-3'
                                                        >
                                                            Chi nhánh
                                                        </th>
                                                        <th
                                                            scope='col'
                                                            className='p-3'
                                                        >
                                                            Số tài khoản
                                                        </th>
                                                        <th
                                                            scope='col'
                                                            className='p-3'
                                                        >
                                                            Tên chủ tài khoản
                                                        </th>
                                                        <th
                                                            scope='col'
                                                            className='p-3'
                                                        >
                                                            Mã Swift
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {data?.data?.companyDetailBankAccounts.map(
                                                        (account, index) => (
                                                            <tr
                                                                key={account.id}
                                                            >
                                                                <td className='p-3'>
                                                                    {index + 1}
                                                                </td>
                                                                <td className='p-3'>
                                                                    {
                                                                        account.bank
                                                                    }
                                                                </td>
                                                                <td className='p-3'>
                                                                    {
                                                                        account.bankBranch
                                                                    }
                                                                </td>
                                                                <td className='p-3'>
                                                                    {
                                                                        account.accountNumber
                                                                    }
                                                                </td>
                                                                <td className='p-3'>
                                                                    {
                                                                        account.accountHolderName
                                                                    }
                                                                </td>
                                                                <td className='p-3'>
                                                                    {
                                                                        account.customSwiftCode
                                                                    }
                                                                </td>
                                                            </tr>
                                                        ),
                                                    )}
                                                </tbody>
                                            </Table>
                                        </div>
                                    </TabPane>
                                </TabContent>
                            </CardBody>
                        </Card>
                    </Col>

                    <Evaluate />
                </Col>

                {data?.data && <FileInfo data={data.data} />}
            </Row>
            <DeleteConfirmModal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                onConfirm={handleConfirmDelete}
                itemName={data?.data?.name}
                loading={isPending}
            />
        </div>
    );
}

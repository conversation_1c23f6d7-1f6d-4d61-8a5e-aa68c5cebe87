'use client';

import { ICustomer } from '@/apis/customer/customer.type';
import { useForm } from 'react-hook-form';
import { <PERSON><PERSON>, Card } from 'reactstrap';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { InferType } from 'yup';

import { FormProvider } from 'react-hook-form';
import {
    AssociatedInfoType,
    FormOfPurchase,
    LeadStatusEnum,
    LifecycleStageEnum,
} from '@/constants/sharedData/sharedData.enums';
import MainInfo from './MainInfo';
import LinkedInfo from './LinkedInfo';
import AdditionalInfo from './AdditionalInfo';

interface UpdateFormProps {
    initValue: ICustomer;
    onEdit: (data: ICustomer) => void;
    onCancel: () => void;
}

const customerFormSchema = yup.object().shape({
    name: yup.string().required('Tên khách hàng là trường bắt buộc'),
    businessTypeId: yup.string().required('<PERSON>ạ<PERSON> hình là trường bắt buộc'),
    industryId: yup.string().required('Lĩnh vực là trường bắt buộc'),
    taxCode: yup
        .string()
        .required('Mã số thuế là trường bắt buộc')
        .matches(/^[0-9]{10,13}$/, 'Mã số thuế không hợp lệ'),
    annualRevenue: yup
        .number()
        .required('Doanh thu ước tính là trường bắt buộc')
        .positive('Doanh thu phải là số dương'),

    leadStatus: yup
        .string()
        .required('Trạng thái là trường bắt buộc')
        .test('valid-lead-status', 'Trạng thái không hợp lệ', function (value) {
            if (!value) {
                return false;
            }

            const validValues = Object.values(LeadStatusEnum).map((v) =>
                v.toString(),
            );
            return validValues.includes(value);
        }),

    lifecycleStageEnum: yup
        .string()
        .required('Giai đoạn là trường bắt buộc')
        .test(
            'valid-lifecycle-stage',
            'Giai đoạn không hợp lệ',
            function (value) {
                if (!value) {
                    return false;
                }

                const validValues = Object.values(LifecycleStageEnum).map((v) =>
                    v.toString(),
                );
                return validValues.includes(value);
            },
        ),

    // Thêm validation cho tradePartners
    tradePartners: yup
        .array()
        .of(
            yup.object().shape({
                tradePartnerId: yup
                    .string()
                    .required('ID đối tác thương mại là bắt buộc')
                    .test(
                        'valid-partner-id',
                        'Vui lòng chọn đối tác thương mại hợp lệ',
                        function (value) {
                            // Không cho phép ID tạm thời (từ Date.now())
                            if (!value) {
                                return false;
                            }
                            // Kiểm tra xem có phải là ID tạm thời không (chỉ chứa số và có độ dài > 10)
                            const isTemporaryId =
                                /^\d+$/.test(value) && value.length > 10;
                            return !isTemporaryId;
                        },
                    ),
            }),
        )
        .default([]),

    // Validation cho tài khoản ngân hàng
    companyDetailBankAccounts: yup
        .array()
        .of(
            yup.object().shape({
                bank: yup.string(),
                bankBranch: yup.string(),
                accountNumber: yup.string(),
                accountHolderName: yup.string(),
                customSwiftCode: yup.string(),
            }),
        )
        .default([]),

    // Validation cho địa chỉ
    addresses: yup
        .array()
        .of(
            yup.object().shape({
                addressName: yup.string(),
                provinceId: yup.string(),
                districtId: yup.string(),
                wardId: yup.string(),
                addressType: yup.number(),
                country: yup.string(),
            }),
        )
        .default([]),

    associatedInfoDtos: yup
        .array()
        .of(
            yup.object().shape({
                value: yup.string().when('associatedInfoType', {
                    is: AssociatedInfoType.Email,
                    then: (schema) =>
                        schema
                            .test(
                                'email-required',
                                'Email là trường bắt buộc',
                                function (value) {
                                    return value && value.trim() !== '';
                                },
                            )
                            .test(
                                'email-format',
                                'Email không hợp lệ',
                                function (value) {
                                    if (!value || value.trim() === '') {
                                        return true;
                                    }

                                    const emailRegex =
                                        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                                    return emailRegex.test(value);
                                },
                            ),
                    otherwise: (schema) =>
                        schema
                            .test(
                                'phone-required',
                                'Số điện thoại là trường bắt buộc',
                                function (value) {
                                    // Kiểm tra bắt buộc nhập
                                    return value && value.trim() !== '';
                                },
                            )
                            .test(
                                'phone-format',
                                'Số điện thoại không hợp lệ (VD: 0901234567)',
                                function (value) {
                                    // Nếu chưa nhập gì thì bỏ qua validation format (đã có validation required ở trên)
                                    if (!value || value.trim() === '') {
                                        return true;
                                    }
                                    // Nếu đã nhập thì kiểm tra định dạng số điện thoại
                                    const phoneRegex =
                                        /^(\+84|84|0)(3[2-9]|5[6|8|9]|7[0|6-9]|8[1-9]|9[0-9])[0-9]{7}$/;
                                    return phoneRegex.test(value);
                                },
                            ),
                }),
            }),
        )
        .test(
            'at-least-one-contact',
            'Phải có ít nhất một thông tin liên hệ (email hoặc số điện thoại)',
            function (value) {
                if (!value || !Array.isArray(value)) {
                    return false;
                }
                return value.some(
                    (item) => item.value && item.value.trim() !== '',
                );
            },
        ),
});

type CustomerFormData = InferType<typeof customerFormSchema>;

const UpdateForm = ({ initValue, onEdit, onCancel }: UpdateFormProps) => {
    const defaultValues = {
        associatedInfoDtos: [
            { value: '', associatedInfoType: AssociatedInfoType.Email },
            { value: '', associatedInfoType: AssociatedInfoType.PhoneNumber },
        ],
        formOfPurchase: FormOfPurchase.BuyDirectly,
    };

    const getFormDefaultValues = () => {
        if (!initValue) {
            return defaultValues;
        }

        let existingAssociatedInfo = initValue.associatedInfoDtos || [];

        if (existingAssociatedInfo.length === 0 && initValue.associatedInfos) {
            existingAssociatedInfo = initValue.associatedInfos.map((info) => ({
                value: info.value,
                associatedInfoType: Number(info.associatedInfoType),
            }));
        } else {
            existingAssociatedInfo = existingAssociatedInfo.map((info) => ({
                ...info,
                associatedInfoType: Number(info.associatedInfoType),
            }));
        }

        const hasEmail = existingAssociatedInfo.some(
            (info) => info.associatedInfoType === AssociatedInfoType.Email,
        );
        const hasPhone = existingAssociatedInfo.some(
            (info) =>
                info.associatedInfoType === AssociatedInfoType.PhoneNumber,
        );

        const mergedAssociatedInfo = [...existingAssociatedInfo];

        if (!hasEmail) {
            mergedAssociatedInfo.push({
                value: '',
                associatedInfoType: AssociatedInfoType.Email,
            });
        }

        if (!hasPhone) {
            mergedAssociatedInfo.push({
                value: '',
                associatedInfoType: AssociatedInfoType.PhoneNumber,
            });
        }

        // Xử lý dữ liệu đối tác thương mại
        const tradePartners =
            initValue.detailTradePartnerCompanies?.map((partner) => ({
                tradePartnerId: partner.id,
                name: partner.name,
                email: partner.email,
                phoneNumber: partner.phoneNumber,
            })) || [];

        // Xử lý dữ liệu liên hệ mua hàng
        const purchaseContacts =
            initValue.purchaseContactDetailCompanies?.map((contact) => ({
                contactId: contact.id,
                role: 0, // CompanyContactRole.Purchase
            })) || [];

        // Xử lý dữ liệu liên hệ sử dụng
        const usageContacts =
            initValue.usageContactDetailCompanies?.map((contact) => ({
                contactId: contact.id,
                role: 1, // CompanyContactRole.Usage
            })) || [];

        const result = {
            ...defaultValues,
            ...initValue,
            associatedInfoDtos: mergedAssociatedInfo,
            tradePartners,
            contacts: [...purchaseContacts, ...usageContacts],
            // Chuyển đổi enum values thành string để phù hợp với form
            leadStatus: initValue.leadStatus?.toString() || '',
            lifecycleStageEnum: initValue.lifecycleStageEnum?.toString() || '',
            formOfPurchase:
                initValue.formOfPurchase || FormOfPurchase.BuyDirectly,
        };

        return result;
    };

    const methods = useForm<CustomerFormData>({
        defaultValues: getFormDefaultValues(),
        resolver: yupResolver(customerFormSchema),
    });

    const handleFormSubmit = (data: CustomerFormData) => {
        onEdit(data as unknown as ICustomer);
    };

    return (
        <FormProvider {...methods}>
            <Card style={{ padding: '20px 40px 20px 40px' }}>
                <MainInfo />
                <LinkedInfo initValue={initValue} />
                <AdditionalInfo initValue={initValue} />

                <div className='d-flex justify-content-end mt-4'>
                    <Button
                        color='danger'
                        className='me-2'
                        type='button'
                        onClick={onCancel}
                    >
                        Hủy
                    </Button>
                    <Button
                        color='success'
                        type='button'
                        onClick={methods.handleSubmit(handleFormSubmit)}
                    >
                        Lưu
                    </Button>
                </div>
            </Card>
        </FormProvider>
    );
};

export default UpdateForm;
